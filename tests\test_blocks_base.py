# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 11:45:00 +08:00]
# Reason: P1-ARCH-002 - 创建积木系统基础测试，验证接口实现正确性
# Principle_Applied: 可测试性设计，单元测试覆盖核心功能
# Optimization: 完整的测试覆盖，确保代码质量
# Architectural_Note (AR): 测试架构支持积木系统验证
# Documentation_Note (DW): 积木系统测试已创建，包含基础功能验证
# }}

"""
积木系统基础测试
测试积木接口、注册系统和连接系统
"""

import pytest
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from blocks import (
    IBlock, BaseBlock, Port, DataType, ExecutionContext, ExecutionResult,
    BlockRegistry, register_block, get_block_registry,
    ConnectionManager, ConnectionPoint, Connection
)


class TestBlock(BaseBlock):
    """测试用积木类"""
    
    def __init__(self, block_id: str = ""):
        super().__init__(block_id)
        self._name = "测试积木"
        self._description = "用于测试的积木"
        self._category = "测试"

    def get_inputs(self):
        return [
            Port("input1", DataType.STRING, True, "字符串输入"),
            Port("input2", DataType.INTEGER, False, "整数输入", 0)
        ]

    def get_outputs(self):
        return [
            Port("output1", DataType.STRING, True, "字符串输出")
        ]

    def execute(self, context, inputs):
        input1 = inputs.get("input1", "")
        input2 = inputs.get("input2", 0)
        result = f"{input1}_{input2}"
        return ExecutionResult(True, {"output1": result})


class TestPort:
    """测试Port类"""
    
    def test_port_creation(self):
        """测试端口创建"""
        port = Port("test_port", DataType.STRING, True, "测试端口")
        assert port.name == "test_port"
        assert port.data_type == DataType.STRING
        assert port.required == True
        assert port.description == "测试端口"

    def test_port_compatibility(self):
        """测试端口兼容性"""
        string_port = Port("str", DataType.STRING)
        int_port = Port("int", DataType.INTEGER)
        any_port = Port("any", DataType.ANY)
        
        # 相同类型兼容
        assert string_port.is_compatible(Port("str2", DataType.STRING))
        
        # 不同类型不兼容
        assert not string_port.is_compatible(int_port)
        
        # ANY类型与所有类型兼容
        assert any_port.is_compatible(string_port)
        assert string_port.is_compatible(any_port)

    def test_port_validation(self):
        """测试端口值验证"""
        string_port = Port("str", DataType.STRING, True)
        int_port = Port("int", DataType.INTEGER, False)
        
        # 字符串端口验证
        assert string_port.validate_value("hello")
        assert not string_port.validate_value(123)
        assert not string_port.validate_value(None)  # required=True
        
        # 整数端口验证
        assert int_port.validate_value(123)
        assert int_port.validate_value(None)  # required=False


class TestExecutionContext:
    """测试执行上下文"""
    
    def test_context_creation(self):
        """测试上下文创建"""
        context = ExecutionContext({"var1": "value1"})
        assert context.get_variable("var1") == "value1"
        assert context.get_variable("var2", "default") == "default"

    def test_context_variables(self):
        """测试变量操作"""
        context = ExecutionContext({})
        context.set_variable("test", "value")
        assert context.get_variable("test") == "value"

    def test_context_logging(self):
        """测试日志记录"""
        context = ExecutionContext({})
        context.current_block_id = "test_block"
        context.log("测试消息")
        assert len(context.execution_log) == 1
        assert "test_block" in context.execution_log[0]
        assert "测试消息" in context.execution_log[0]


class TestBlockRegistry:
    """测试积木注册系统"""
    
    def setup_method(self):
        """测试前准备"""
        # 清空注册表
        registry = get_block_registry()
        registry.clear_registry()

    def test_block_registration(self):
        """测试积木注册"""
        registry = get_block_registry()
        
        # 注册积木
        success = register_block(TestBlock)
        assert success == True
        
        # 检查注册结果
        assert registry.is_registered("测试积木")
        assert "测试积木" in registry.get_all_blocks()
        assert "测试" in registry.get_all_categories()

    def test_block_creation(self):
        """测试积木创建"""
        registry = get_block_registry()
        register_block(TestBlock)
        
        # 创建积木实例
        block = registry.create_block("测试积木", "test_id")
        assert block is not None
        assert block.block_id == "test_id"
        assert block.get_name() == "测试积木"

    def test_block_info(self):
        """测试积木信息获取"""
        registry = get_block_registry()
        register_block(TestBlock)
        
        info = registry.get_block_info("测试积木")
        assert info is not None
        assert info["name"] == "测试积木"
        assert info["description"] == "用于测试的积木"
        assert info["category"] == "测试"


class TestConnectionManager:
    """测试连接管理器"""
    
    def setup_method(self):
        """测试前准备"""
        self.manager = ConnectionManager()
        
        # 注册测试积木的端口
        test_block = TestBlock()
        self.manager.register_block_ports(
            "block1", 
            test_block.get_inputs(), 
            test_block.get_outputs()
        )
        self.manager.register_block_ports(
            "block2", 
            test_block.get_inputs(), 
            test_block.get_outputs()
        )

    def test_connection_creation(self):
        """测试连接创建"""
        # 创建有效连接
        conn_id = self.manager.create_connection(
            "block1", "output1", "block2", "input1"
        )
        assert conn_id is not None
        
        # 获取连接
        connection = self.manager.get_connection(conn_id)
        assert connection is not None
        assert connection.source.block_id == "block1"
        assert connection.target.block_id == "block2"

    def test_invalid_connection(self):
        """测试无效连接"""
        # 尝试连接到自己
        conn_id = self.manager.create_connection(
            "block1", "output1", "block1", "input1"
        )
        assert conn_id is None
        
        # 尝试连接不存在的端口
        conn_id = self.manager.create_connection(
            "block1", "nonexistent", "block2", "input1"
        )
        assert conn_id is None

    def test_connection_removal(self):
        """测试连接删除"""
        # 创建连接
        conn_id = self.manager.create_connection(
            "block1", "output1", "block2", "input1"
        )
        assert conn_id is not None
        
        # 删除连接
        success = self.manager.remove_connection(conn_id)
        assert success == True
        
        # 验证连接已删除
        connection = self.manager.get_connection(conn_id)
        assert connection is None


if __name__ == "__main__":
    pytest.main([__file__])
