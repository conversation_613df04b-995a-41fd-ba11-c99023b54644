# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 12:00:00 +08:00]
# Reason: P1-GUI-003 - 创建画布组件，基于PyQt Graphics View实现拖拽功能
# Principle_Applied: 单一职责原则 - 画布只负责绘制和交互，高内聚低耦合 - 画布组件独立
# Optimization: 使用Graphics View Framework，支持高性能绘制和缩放
# Architectural_Note (AR): 画布系统核心组件，支持积木拖拽和连接
# Documentation_Note (DW): 画布组件已实现，包含基础拖拽和绘制功能
# }}

"""
画布组件
基于PyQt Graphics View的可视化设计器画布
"""

from typing import Optional, List, Dict, Any
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QGraphicsView, QGraphicsScene, 
    QGraphicsItem, QGraphicsProxyWidget, QUndoStack
)
from PyQt5.QtCore import Qt, pyqtSignal, QPointF, QRectF
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor


class CanvasScene(QGraphicsScene):
    """
    画布场景类
    管理画布上的所有图形项目
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSceneRect(-5000, -5000, 10000, 10000)
        self._setup_scene()
        
    def _setup_scene(self) -> None:
        """设置场景"""
        # 设置背景颜色
        self.setBackgroundBrush(QBrush(QColor(245, 245, 245)))
        
    def drawBackground(self, painter: QPainter, rect: QRectF) -> None:
        """绘制背景网格"""
        super().drawBackground(painter, rect)
        
        # 绘制网格
        grid_size = 20
        pen = QPen(QColor(220, 220, 220))
        pen.setWidth(1)
        painter.setPen(pen)
        
        # 计算网格线的范围
        left = int(rect.left()) - (int(rect.left()) % grid_size)
        top = int(rect.top()) - (int(rect.top()) % grid_size)
        
        # 绘制垂直线
        x = left
        while x < rect.right():
            painter.drawLine(x, rect.top(), x, rect.bottom())
            x += grid_size
            
        # 绘制水平线
        y = top
        while y < rect.bottom():
            painter.drawLine(rect.left(), y, rect.right(), y)
            y += grid_size


class CanvasWidget(QWidget):
    """
    画布组件
    提供可视化设计器的主要工作区域
    """
    
    # 信号定义
    block_selected = pyqtSignal(object)  # 积木选中信号
    block_moved = pyqtSignal(str, QPointF)  # 积木移动信号
    connection_created = pyqtSignal(str, str, str, str)  # 连接创建信号
    status_changed = pyqtSignal(str)  # 状态变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._blocks: Dict[str, Any] = {}  # 积木实例字典
        self._connections: List[Any] = []  # 连接列表
        self._undo_stack = QUndoStack(self)
        self._setup_ui()
        
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建场景和视图
        self.scene = CanvasScene()
        self.view = QGraphicsView(self.scene)
        
        # 设置视图属性
        self.view.setRenderHint(QPainter.Antialiasing)
        self.view.setDragMode(QGraphicsView.RubberBandDrag)
        self.view.setViewportUpdateMode(QGraphicsView.FullViewportUpdate)
        
        # 启用拖放
        self.view.setAcceptDrops(True)
        self.setAcceptDrops(True)
        
        layout.addWidget(self.view)
        
        # 连接信号
        self.scene.selectionChanged.connect(self._on_selection_changed)
        
    def _on_selection_changed(self) -> None:
        """处理选择变化"""
        selected_items = self.scene.selectedItems()
        if selected_items:
            # 发送选中的第一个积木
            item = selected_items[0]
            if hasattr(item, 'block_data'):
                self.block_selected.emit(item.block_data)
        else:
            self.block_selected.emit(None)
            
    def add_block(self, block_type: str, position: QPointF, block_id: str = "") -> Optional[str]:
        """
        添加积木到画布
        
        Args:
            block_type: 积木类型
            position: 位置
            block_id: 积木ID
            
        Returns:
            Optional[str]: 积木ID，如果添加失败返回None
        """
        try:
            # TODO: 从积木注册器创建积木实例
            # from blocks import get_block_registry
            # registry = get_block_registry()
            # block = registry.create_block(block_type, block_id)
            
            # 临时创建一个简单的积木表示
            from PyQt5.QtWidgets import QGraphicsRectItem, QGraphicsTextItem
            from PyQt5.QtGui import QBrush, QPen
            
            # 创建积木图形项
            block_item = QGraphicsRectItem(0, 0, 120, 60)
            block_item.setBrush(QBrush(QColor(100, 150, 200)))
            block_item.setPen(QPen(QColor(50, 100, 150), 2))
            block_item.setFlag(QGraphicsItem.ItemIsMovable)
            block_item.setFlag(QGraphicsItem.ItemIsSelectable)
            block_item.setPos(position)
            
            # 添加文本标签
            text_item = QGraphicsTextItem(block_type, block_item)
            text_item.setPos(10, 20)
            
            # 添加到场景
            self.scene.addItem(block_item)
            
            # 保存积木数据
            if not block_id:
                block_id = f"block_{len(self._blocks) + 1}"
            
            block_data = {
                'id': block_id,
                'type': block_type,
                'position': position,
                'item': block_item
            }
            
            block_item.block_data = block_data
            self._blocks[block_id] = block_data
            
            self.status_changed.emit(f"添加积木: {block_type}")
            return block_id
            
        except Exception as e:
            self.status_changed.emit(f"添加积木失败: {e}")
            return None
            
    def remove_block(self, block_id: str) -> bool:
        """
        删除积木
        
        Args:
            block_id: 积木ID
            
        Returns:
            bool: 是否删除成功
        """
        if block_id not in self._blocks:
            return False
            
        try:
            block_data = self._blocks[block_id]
            self.scene.removeItem(block_data['item'])
            del self._blocks[block_id]
            
            self.status_changed.emit(f"删除积木: {block_id}")
            return True
            
        except Exception as e:
            self.status_changed.emit(f"删除积木失败: {e}")
            return False
            
    def clear_canvas(self) -> None:
        """清空画布"""
        self.scene.clear()
        self._blocks.clear()
        self._connections.clear()
        self._undo_stack.clear()
        self.status_changed.emit("画布已清空")
        
    def get_blocks(self) -> Dict[str, Any]:
        """获取所有积木"""
        return self._blocks.copy()
        
    def get_connections(self) -> List[Any]:
        """获取所有连接"""
        return self._connections.copy()
        
    def zoom_in(self) -> None:
        """放大视图"""
        self.view.scale(1.2, 1.2)
        
    def zoom_out(self) -> None:
        """缩小视图"""
        self.view.scale(0.8, 0.8)
        
    def zoom_fit(self) -> None:
        """适应窗口大小"""
        self.view.fitInView(self.scene.itemsBoundingRect(), Qt.KeepAspectRatio)
        
    def zoom_reset(self) -> None:
        """重置缩放"""
        self.view.resetTransform()
        
    def undo(self) -> None:
        """撤销操作"""
        self._undo_stack.undo()
        
    def redo(self) -> None:
        """重做操作"""
        self._undo_stack.redo()
        
    def prepare_for_drop(self, block_type: str) -> None:
        """
        准备接收拖放
        
        Args:
            block_type: 积木类型
        """
        self.status_changed.emit(f"准备添加积木: {block_type}")
        
    def dragEnterEvent(self, event) -> None:
        """拖拽进入事件"""
        if event.mimeData().hasText():
            event.acceptProposedAction()
            
    def dragMoveEvent(self, event) -> None:
        """拖拽移动事件"""
        if event.mimeData().hasText():
            event.acceptProposedAction()
            
    def dropEvent(self, event) -> None:
        """拖放事件"""
        if event.mimeData().hasText():
            block_type = event.mimeData().text()
            
            # 将窗口坐标转换为场景坐标
            scene_pos = self.view.mapToScene(event.pos())
            
            # 添加积木
            block_id = self.add_block(block_type, scene_pos)
            if block_id:
                event.acceptProposedAction()
            else:
                event.ignore()
                
    def export_to_config(self) -> Dict[str, Any]:
        """
        导出为配置文件格式
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        config = {
            'blocks': [],
            'connections': []
        }
        
        # 导出积木
        for block_id, block_data in self._blocks.items():
            block_config = {
                'id': block_id,
                'type': block_data['type'],
                'position': {
                    'x': block_data['position'].x(),
                    'y': block_data['position'].y()
                },
                'config': {}  # TODO: 添加积木配置
            }
            config['blocks'].append(block_config)
            
        # 导出连接
        for connection in self._connections:
            # TODO: 实现连接导出
            pass
            
        return config
        
    def load_from_config(self, config: Dict[str, Any]) -> bool:
        """
        从配置文件加载
        
        Args:
            config: 配置数据
            
        Returns:
            bool: 是否加载成功
        """
        try:
            self.clear_canvas()
            
            # 加载积木
            for block_config in config.get('blocks', []):
                position = QPointF(
                    block_config['position']['x'],
                    block_config['position']['y']
                )
                self.add_block(
                    block_config['type'],
                    position,
                    block_config['id']
                )
                
            # 加载连接
            # TODO: 实现连接加载
            
            self.status_changed.emit("配置加载完成")
            return True
            
        except Exception as e:
            self.status_changed.emit(f"配置加载失败: {e}")
            return False
