# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 11:45:00 +08:00]
# Reason: P1-ARCH-002 - 创建简化测试验证积木系统基础功能
# Principle_Applied: KISS原则 - 简化测试逻辑，快速验证核心功能
# Optimization: 直接验证导入和基础功能，避免复杂测试框架
# Architectural_Note (AR): 简化验证积木系统实现正确性
# Documentation_Note (DW): 简化测试脚本，用于快速验证积木系统
# }}

"""
积木系统简化测试
快速验证积木系统基础功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """测试导入"""
    try:
        from blocks import (
            IBlock, BaseBlock, Port, DataType, ExecutionContext, ExecutionResult,
            BlockRegistry, register_block, get_block_registry,
            ConnectionManager, ConnectionPoint, Connection
        )
        print("✓ 积木系统导入成功")
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_port_creation():
    """测试端口创建"""
    try:
        from blocks import Port, DataType
        
        port = Port("test_port", DataType.STRING, True, "测试端口")
        assert port.name == "test_port"
        assert port.data_type == DataType.STRING
        print("✓ 端口创建测试通过")
        return True
    except Exception as e:
        print(f"✗ 端口创建测试失败: {e}")
        return False

def test_registry():
    """测试注册系统"""
    try:
        from blocks import BaseBlock, Port, DataType, ExecutionContext, ExecutionResult, register_block, get_block_registry
        
        class TestBlock(BaseBlock):
            def __init__(self, block_id: str = ""):
                super().__init__(block_id)
                self._name = "测试积木"
                self._description = "用于测试的积木"
                self._category = "测试"

            def get_inputs(self):
                return [Port("input1", DataType.STRING)]

            def get_outputs(self):
                return [Port("output1", DataType.STRING)]

            def execute(self, context, inputs):
                return ExecutionResult(True, {"output1": "test"})
        
        # 清空注册表
        registry = get_block_registry()
        registry.clear_registry()
        
        # 注册积木
        success = register_block(TestBlock)
        assert success == True
        
        # 验证注册
        assert registry.is_registered("测试积木")
        
        # 创建实例
        block = registry.create_block("测试积木", "test_id")
        assert block is not None
        assert block.get_name() == "测试积木"
        
        print("✓ 注册系统测试通过")
        return True
    except Exception as e:
        print(f"✗ 注册系统测试失败: {e}")
        return False

def test_connection_manager():
    """测试连接管理器"""
    try:
        from blocks import ConnectionManager, Port, DataType
        
        manager = ConnectionManager()
        
        # 注册端口
        input_ports = [Port("input1", DataType.STRING)]
        output_ports = [Port("output1", DataType.STRING)]
        
        manager.register_block_ports("block1", input_ports, output_ports)
        manager.register_block_ports("block2", input_ports, output_ports)
        
        # 创建连接
        conn_id = manager.create_connection("block1", "output1", "block2", "input1")
        assert conn_id is not None
        
        # 获取连接
        connection = manager.get_connection(conn_id)
        assert connection is not None
        
        print("✓ 连接管理器测试通过")
        return True
    except Exception as e:
        print(f"✗ 连接管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("积木系统基础功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_port_creation,
        test_registry,
        test_connection_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！积木系统基础架构实现正确")
        return True
    else:
        print("✗ 部分测试失败，需要检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
