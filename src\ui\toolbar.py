# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 12:00:00 +08:00]
# Reason: P1-GUI-003 - 创建积木工具栏，提供积木库和拖拽功能
# Principle_Applied: 单一职责原则 - 工具栏只负责积木展示和拖拽，接口隔离原则 - 简化工具栏接口
# Optimization: 分类展示积木，支持搜索和过滤功能
# Architectural_Note (AR): 积木工具栏组件，支持积木库管理
# Documentation_Note (DW): 积木工具栏已实现，包含分类展示和拖拽功能
# }}

"""
积木工具栏
提供积木库的展示和拖拽功能
"""

from typing import Dict, List, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QTreeWidget, QTreeWidgetItem, QPushButton, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, QMimeData
from PyQt5.QtGui import QDrag, QPainter, QPixmap, QColor, QBrush, QPen


class BlockItem(QTreeWidgetItem):
    """
    积木项目类
    表示工具栏中的一个积木
    """
    
    def __init__(self, parent, block_name: str, block_description: str):
        super().__init__(parent)
        self.block_name = block_name
        self.block_description = block_description
        
        self.setText(0, block_name)
        self.setToolTip(0, block_description)
        
        # 设置项目属性
        self.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)


class BlockCategoryItem(QTreeWidgetItem):
    """
    积木分类项目类
    表示积木的分类
    """
    
    def __init__(self, parent, category_name: str):
        super().__init__(parent)
        self.category_name = category_name
        
        self.setText(0, category_name)
        self.setExpanded(True)
        
        # 设置分类样式
        font = self.font(0)
        font.setBold(True)
        self.setFont(0, font)


class BlockToolbar(QWidget):
    """
    积木工具栏
    提供积木库的展示和管理功能
    """
    
    # 信号定义
    block_drag_started = pyqtSignal(str)  # 积木拖拽开始信号
    block_selected = pyqtSignal(str)      # 积木选中信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._block_categories: Dict[str, List[str]] = {}
        self._block_info: Dict[str, Dict[str, str]] = {}
        self._setup_ui()
        self._load_default_blocks()
        
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        title_label = QLabel("积木库")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                background-color: #e0e0e0;
                border-radius: 3px;
            }
        """)
        layout.addWidget(title_label)
        
        # 搜索框
        search_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索积木...")
        self.search_edit.textChanged.connect(self._filter_blocks)
        search_layout.addWidget(self.search_edit)
        
        layout.addLayout(search_layout)
        
        # 积木树
        self.block_tree = QTreeWidget()
        self.block_tree.setHeaderHidden(True)
        self.block_tree.setRootIsDecorated(True)
        self.block_tree.setDragEnabled(True)
        self.block_tree.setDragDropMode(QTreeWidget.DragOnly)
        
        # 连接信号
        self.block_tree.itemClicked.connect(self._on_item_clicked)
        self.block_tree.itemDoubleClicked.connect(self._on_item_double_clicked)
        
        layout.addWidget(self.block_tree)
        
        # 刷新按钮
        refresh_button = QPushButton("刷新积木库")
        refresh_button.clicked.connect(self._refresh_blocks)
        layout.addWidget(refresh_button)
        
    def _load_default_blocks(self) -> None:
        """加载默认积木"""
        # 基础操作积木
        self.add_block_category("基础操作", [
            ("点击积木", "点击网页元素"),
            ("填写积木", "填写输入框内容"),
            ("等待积木", "等待指定时间"),
            ("截图积木", "截取屏幕截图")
        ])
        
        # 数据处理积木
        self.add_block_category("数据处理", [
            ("提取积木", "提取网页数据"),
            ("保存积木", "保存数据到文件"),
            ("计算积木", "执行数学计算")
        ])
        
        # 逻辑控制积木
        self.add_block_category("逻辑控制", [
            ("判断积木", "条件判断"),
            ("循环积木", "循环执行"),
            ("跳转积木", "跳转到指定位置")
        ])
        
        self._refresh_tree()
        
    def add_block_category(self, category: str, blocks: List[tuple]) -> None:
        """
        添加积木分类
        
        Args:
            category: 分类名称
            blocks: 积木列表，每个元素为(名称, 描述)元组
        """
        if category not in self._block_categories:
            self._block_categories[category] = []
            
        for block_name, block_description in blocks:
            self._block_categories[category].append(block_name)
            self._block_info[block_name] = {
                "name": block_name,
                "description": block_description,
                "category": category
            }
            
    def _refresh_tree(self) -> None:
        """刷新积木树"""
        self.block_tree.clear()
        
        for category, blocks in self._block_categories.items():
            category_item = BlockCategoryItem(self.block_tree, category)
            
            for block_name in blocks:
                if block_name in self._block_info:
                    block_info = self._block_info[block_name]
                    block_item = BlockItem(
                        category_item, 
                        block_name, 
                        block_info["description"]
                    )
                    
    def _filter_blocks(self, text: str) -> None:
        """
        过滤积木
        
        Args:
            text: 搜索文本
        """
        if not text:
            self._refresh_tree()
            return
            
        self.block_tree.clear()
        text = text.lower()
        
        for category, blocks in self._block_categories.items():
            category_item = None
            
            for block_name in blocks:
                if block_name in self._block_info:
                    block_info = self._block_info[block_name]
                    
                    # 检查是否匹配搜索条件
                    if (text in block_name.lower() or 
                        text in block_info["description"].lower()):
                        
                        # 创建分类项（如果还没有）
                        if category_item is None:
                            category_item = BlockCategoryItem(self.block_tree, category)
                            
                        # 添加积木项
                        block_item = BlockItem(
                            category_item,
                            block_name,
                            block_info["description"]
                        )
                        
    def _on_item_clicked(self, item: QTreeWidgetItem, column: int) -> None:
        """处理项目点击"""
        if isinstance(item, BlockItem):
            self.block_selected.emit(item.block_name)
            
    def _on_item_double_clicked(self, item: QTreeWidgetItem, column: int) -> None:
        """处理项目双击"""
        if isinstance(item, BlockItem):
            # 双击时开始拖拽
            self._start_drag(item.block_name)
            
    def _start_drag(self, block_name: str) -> None:
        """
        开始拖拽操作
        
        Args:
            block_name: 积木名称
        """
        # 创建拖拽数据
        mime_data = QMimeData()
        mime_data.setText(block_name)
        
        # 创建拖拽图标
        pixmap = self._create_drag_pixmap(block_name)
        
        # 开始拖拽
        drag = QDrag(self)
        drag.setMimeData(mime_data)
        drag.setPixmap(pixmap)
        drag.setHotSpot(pixmap.rect().center())
        
        # 发送信号
        self.block_drag_started.emit(block_name)
        
        # 执行拖拽
        drag.exec_(Qt.CopyAction)
        
    def _create_drag_pixmap(self, block_name: str) -> QPixmap:
        """
        创建拖拽图标
        
        Args:
            block_name: 积木名称
            
        Returns:
            QPixmap: 拖拽图标
        """
        # 创建一个简单的积木图标
        pixmap = QPixmap(120, 60)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制积木背景
        brush = QBrush(QColor(100, 150, 200, 180))
        pen = QPen(QColor(50, 100, 150), 2)
        painter.setBrush(brush)
        painter.setPen(pen)
        painter.drawRoundedRect(0, 0, 120, 60, 5, 5)
        
        # 绘制文本
        painter.setPen(QColor(255, 255, 255))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, block_name)
        
        painter.end()
        return pixmap
        
    def _refresh_blocks(self) -> None:
        """刷新积木库"""
        # TODO: 从积木注册器重新加载积木
        # from blocks import get_block_registry
        # registry = get_block_registry()
        # self._load_blocks_from_registry(registry)
        
        self._refresh_tree()
        
    def get_block_info(self, block_name: str) -> Optional[Dict[str, str]]:
        """
        获取积木信息
        
        Args:
            block_name: 积木名称
            
        Returns:
            Optional[Dict[str, str]]: 积木信息
        """
        return self._block_info.get(block_name)
        
    def get_all_categories(self) -> List[str]:
        """
        获取所有分类
        
        Returns:
            List[str]: 分类列表
        """
        return list(self._block_categories.keys())
        
    def get_blocks_in_category(self, category: str) -> List[str]:
        """
        获取指定分类的积木
        
        Args:
            category: 分类名称
            
        Returns:
            List[str]: 积木列表
        """
        return self._block_categories.get(category, []).copy()
