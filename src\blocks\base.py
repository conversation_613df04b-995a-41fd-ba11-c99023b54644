# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 11:45:00 +08:00]
# Reason: P1-ARCH-002 - 实现积木系统基础接口，建立积木标准
# Principle_Applied: 单一职责原则 - IBlock接口职责明确，开闭原则 - 支持积木扩展，接口隔离原则 - 接口精简
# Optimization: 使用抽象基类确保接口一致性，类型注解提高代码安全性
# Architectural_Note (AR): 积木系统核心接口，符合架构设计中的IBlock定义
# Documentation_Note (DW): 积木基础接口已实现，包含完整的类型注解和文档
# }}

"""
积木系统基础接口
定义所有积木的标准接口和基础功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, Union
from enum import Enum
from dataclasses import dataclass


class DataType(Enum):
    """数据类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    LIST = "list"
    DICT = "dict"
    ANY = "any"
    ELEMENT = "element"  # 网页元素
    FILE = "file"        # 文件路径


@dataclass
class Port:
    """
    积木端口类
    用于定义积木的输入输出接口
    """
    name: str
    data_type: DataType
    required: bool = True
    description: str = ""
    default_value: Any = None

    def is_compatible(self, other_port: 'Port') -> bool:
        """
        检查端口兼容性
        
        Args:
            other_port: 另一个端口
            
        Returns:
            bool: 是否兼容
        """
        # ANY类型与所有类型兼容
        if self.data_type == DataType.ANY or other_port.data_type == DataType.ANY:
            return True
        
        # 相同类型兼容
        return self.data_type == other_port.data_type

    def validate_value(self, value: Any) -> bool:
        """
        验证值是否符合端口类型要求
        
        Args:
            value: 要验证的值
            
        Returns:
            bool: 是否有效
        """
        if value is None:
            return not self.required
        
        if self.data_type == DataType.ANY:
            return True
        elif self.data_type == DataType.STRING:
            return isinstance(value, str)
        elif self.data_type == DataType.INTEGER:
            return isinstance(value, int)
        elif self.data_type == DataType.FLOAT:
            return isinstance(value, (int, float))
        elif self.data_type == DataType.BOOLEAN:
            return isinstance(value, bool)
        elif self.data_type == DataType.LIST:
            return isinstance(value, list)
        elif self.data_type == DataType.DICT:
            return isinstance(value, dict)
        
        return False


@dataclass
class ExecutionContext:
    """
    执行上下文
    包含积木执行时的环境信息
    """
    variables: Dict[str, Any]
    browser: Optional[Any] = None
    current_block_id: Optional[str] = None
    execution_log: List[str] = None
    
    def __post_init__(self):
        if self.execution_log is None:
            self.execution_log = []

    def get_variable(self, name: str, default: Any = None) -> Any:
        """获取变量值"""
        return self.variables.get(name, default)

    def set_variable(self, name: str, value: Any) -> None:
        """设置变量值"""
        self.variables[name] = value

    def log(self, message: str) -> None:
        """记录执行日志"""
        self.execution_log.append(f"[{self.current_block_id}] {message}")


class ExecutionResult:
    """
    执行结果类
    """
    def __init__(self, success: bool, data: Any = None, error: str = ""):
        self.success = success
        self.data = data
        self.error = error

    def __bool__(self) -> bool:
        return self.success


class IBlock(ABC):
    """
    积木基础接口
    所有积木类型都必须实现此接口
    """
    
    def __init__(self, block_id: str = ""):
        self.block_id = block_id
        self.config: Dict[str, Any] = {}

    @abstractmethod
    def get_name(self) -> str:
        """
        获取积木名称
        
        Returns:
            str: 积木名称
        """
        pass

    @abstractmethod
    def get_description(self) -> str:
        """
        获取积木描述
        
        Returns:
            str: 积木描述
        """
        pass

    @abstractmethod
    def get_category(self) -> str:
        """
        获取积木分类
        
        Returns:
            str: 积木分类
        """
        pass

    @abstractmethod
    def get_inputs(self) -> List[Port]:
        """
        获取输入端口列表
        
        Returns:
            List[Port]: 输入端口列表
        """
        pass

    @abstractmethod
    def get_outputs(self) -> List[Port]:
        """
        获取输出端口列表
        
        Returns:
            List[Port]: 输出端口列表
        """
        pass

    @abstractmethod
    def execute(self, context: ExecutionContext, inputs: Dict[str, Any]) -> ExecutionResult:
        """
        执行积木逻辑
        
        Args:
            context: 执行上下文
            inputs: 输入数据
            
        Returns:
            ExecutionResult: 执行结果
        """
        pass

    def validate_config(self) -> bool:
        """
        验证积木配置
        
        Returns:
            bool: 配置是否有效
        """
        return True

    def get_config_schema(self) -> Dict[str, Any]:
        """
        获取配置模式定义
        用于动态生成配置界面
        
        Returns:
            Dict[str, Any]: 配置模式
        """
        return {}

    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置积木配置
        
        Args:
            config: 配置数据
        """
        self.config = config.copy()

    def get_config(self) -> Dict[str, Any]:
        """
        获取积木配置
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        return self.config.copy()


class BaseBlock(IBlock):
    """
    积木基类
    提供通用的积木功能实现
    """
    
    def __init__(self, block_id: str = ""):
        super().__init__(block_id)
        self._name = ""
        self._description = ""
        self._category = ""

    def get_name(self) -> str:
        return self._name

    def get_description(self) -> str:
        return self._description

    def get_category(self) -> str:
        return self._category

    def validate_inputs(self, inputs: Dict[str, Any]) -> bool:
        """
        验证输入数据
        
        Args:
            inputs: 输入数据
            
        Returns:
            bool: 输入是否有效
        """
        input_ports = self.get_inputs()
        
        for port in input_ports:
            value = inputs.get(port.name)
            if not port.validate_value(value):
                return False
        
        return True

    def log_execution(self, context: ExecutionContext, message: str) -> None:
        """
        记录执行日志
        
        Args:
            context: 执行上下文
            message: 日志消息
        """
        context.log(f"{self.get_name()}: {message}")
