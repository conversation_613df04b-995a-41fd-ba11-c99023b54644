# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 12:00:00 +08:00]
# Reason: P1-GUI-003 - 创建PyQt主窗口，建立应用程序UI框架
# Principle_Applied: 单一职责原则 - 主窗口只负责布局管理，高内聚低耦合 - UI组件独立
# Optimization: 模块化UI设计，支持组件独立开发和测试
# Architectural_Note (AR): 主窗口框架，符合UI层架构设计
# Documentation_Note (DW): 主窗口类已实现，包含完整的布局管理和组件集成
# }}

"""
主窗口类
应用程序的主界面，负责整体布局和组件管理
"""

import sys
from typing import Optional
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QMenuBar, QToolBar, QStatusBar, QAction, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QKeySequence

from .canvas_widget import CanvasWidget
from .toolbar import BlockToolbar
from .property_editor import PropertyEditor
from .log_viewer import LogViewer


class MainWindow(QMainWindow):
    """
    主窗口类
    管理应用程序的整体界面布局
    """
    
    # 信号定义
    project_opened = pyqtSignal(str)  # 项目打开信号
    project_saved = pyqtSignal(str)   # 项目保存信号
    flow_executed = pyqtSignal()      # 流程执行信号
    
    def __init__(self):
        super().__init__()
        self.current_project_path: Optional[str] = None
        self._setup_ui()
        self._setup_menu()
        self._setup_toolbar()
        self._setup_status_bar()
        self._connect_signals()
        
    def _setup_ui(self) -> None:
        """设置用户界面"""
        self.setWindowTitle("内网RPA工具 - 积木式可视化自动化工具")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # 左侧：积木工具栏
        self.block_toolbar = BlockToolbar()
        self.block_toolbar.setMaximumWidth(250)
        self.block_toolbar.setMinimumWidth(200)
        main_splitter.addWidget(self.block_toolbar)
        
        # 中央：画布区域
        self.canvas_widget = CanvasWidget()
        main_splitter.addWidget(self.canvas_widget)
        
        # 右侧：属性编辑器和日志查看器
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.setMaximumWidth(300)
        right_splitter.setMinimumWidth(250)
        
        self.property_editor = PropertyEditor()
        self.log_viewer = LogViewer()
        
        right_splitter.addWidget(self.property_editor)
        right_splitter.addWidget(self.log_viewer)
        
        main_splitter.addWidget(right_splitter)
        
        # 设置分割器比例
        main_splitter.setSizes([250, 800, 300])
        right_splitter.setSizes([400, 400])
        
    def _setup_menu(self) -> None:
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence.New)
        new_action.setStatusTip("创建新的自动化项目")
        new_action.triggered.connect(self._new_project)
        file_menu.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.setStatusTip("打开现有项目")
        open_action.triggered.connect(self._open_project)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 保存项目
        save_action = QAction("保存项目(&S)", self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.setStatusTip("保存当前项目")
        save_action.triggered.connect(self._save_project)
        file_menu.addAction(save_action)
        
        # 另存为
        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut(QKeySequence.SaveAs)
        save_as_action.setStatusTip("将项目保存到新位置")
        save_as_action.triggered.connect(self._save_project_as)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 撤销
        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut(QKeySequence.Undo)
        undo_action.setStatusTip("撤销上一步操作")
        undo_action.triggered.connect(self._undo)
        edit_menu.addAction(undo_action)
        
        # 重做
        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut(QKeySequence.Redo)
        redo_action.setStatusTip("重做上一步操作")
        redo_action.triggered.connect(self._redo)
        edit_menu.addAction(redo_action)
        
        # 运行菜单
        run_menu = menubar.addMenu("运行(&R)")
        
        # 执行流程
        execute_action = QAction("执行流程(&E)", self)
        execute_action.setShortcut("F5")
        execute_action.setStatusTip("执行当前自动化流程")
        execute_action.triggered.connect(self._execute_flow)
        run_menu.addAction(execute_action)
        
        # 停止执行
        stop_action = QAction("停止执行(&S)", self)
        stop_action.setShortcut("Shift+F5")
        stop_action.setStatusTip("停止当前执行")
        stop_action.triggered.connect(self._stop_execution)
        run_menu.addAction(stop_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 用户指南
        guide_action = QAction("用户指南(&G)", self)
        guide_action.setStatusTip("查看用户使用指南")
        guide_action.triggered.connect(self._show_user_guide)
        help_menu.addAction(guide_action)
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于本软件")
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
        
    def _setup_toolbar(self) -> None:
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # 新建
        new_action = QAction("新建", self)
        new_action.setStatusTip("创建新项目")
        new_action.triggered.connect(self._new_project)
        toolbar.addAction(new_action)
        
        # 打开
        open_action = QAction("打开", self)
        open_action.setStatusTip("打开项目")
        open_action.triggered.connect(self._open_project)
        toolbar.addAction(open_action)
        
        # 保存
        save_action = QAction("保存", self)
        save_action.setStatusTip("保存项目")
        save_action.triggered.connect(self._save_project)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # 执行
        execute_action = QAction("执行", self)
        execute_action.setStatusTip("执行流程")
        execute_action.triggered.connect(self._execute_flow)
        toolbar.addAction(execute_action)
        
        # 停止
        stop_action = QAction("停止", self)
        stop_action.setStatusTip("停止执行")
        stop_action.triggered.connect(self._stop_execution)
        toolbar.addAction(stop_action)
        
    def _setup_status_bar(self) -> None:
        """设置状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")
        
    def _connect_signals(self) -> None:
        """连接信号槽"""
        # 画布信号连接
        self.canvas_widget.block_selected.connect(self.property_editor.set_block)
        self.canvas_widget.status_changed.connect(self.status_bar.showMessage)
        
        # 积木工具栏信号连接
        self.block_toolbar.block_drag_started.connect(self.canvas_widget.prepare_for_drop)
        
    # 菜单动作实现
    def _new_project(self) -> None:
        """新建项目"""
        self.canvas_widget.clear_canvas()
        self.current_project_path = None
        self.setWindowTitle("内网RPA工具 - 新项目")
        self.status_bar.showMessage("新项目已创建")
        
    def _open_project(self) -> None:
        """打开项目"""
        # TODO: 实现项目打开对话框
        self.status_bar.showMessage("打开项目功能待实现")
        
    def _save_project(self) -> None:
        """保存项目"""
        # TODO: 实现项目保存
        self.status_bar.showMessage("保存项目功能待实现")
        
    def _save_project_as(self) -> None:
        """另存为项目"""
        # TODO: 实现另存为功能
        self.status_bar.showMessage("另存为功能待实现")
        
    def _undo(self) -> None:
        """撤销操作"""
        self.canvas_widget.undo()
        
    def _redo(self) -> None:
        """重做操作"""
        self.canvas_widget.redo()
        
    def _execute_flow(self) -> None:
        """执行流程"""
        self.flow_executed.emit()
        self.status_bar.showMessage("开始执行流程...")
        
    def _stop_execution(self) -> None:
        """停止执行"""
        self.status_bar.showMessage("停止执行")
        
    def _show_user_guide(self) -> None:
        """显示用户指南"""
        QMessageBox.information(self, "用户指南", "用户指南功能待实现")
        
    def _show_about(self) -> None:
        """显示关于信息"""
        QMessageBox.about(self, "关于", 
                         "内网RPA工具 v1.0\n\n"
                         "积木式可视化自动化工具\n"
                         "专为小白用户设计\n\n"
                         "© 2025 RPA Development Team")


def create_application() -> QApplication:
    """
    创建Qt应用程序实例
    
    Returns:
        QApplication: Qt应用程序实例
    """
    app = QApplication(sys.argv)
    app.setApplicationName("内网RPA工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("RPA Development Team")
    
    return app
