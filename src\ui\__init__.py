# {{CHENGQI:
# Action: Modified
# Timestamp: [2025-01-27 12:00:00 +08:00]
# Reason: P1-GUI-003 - 更新UI包导出，提供主要UI组件访问
# Principle_Applied: 接口隔离原则 - 只导出必要的公共接口，KISS原则 - 简化导入路径
# Optimization: 统一的UI包接口，便于外部模块使用
# Architectural_Note (AR): UI层公共接口，符合模块化设计
# Documentation_Note (DW): UI包接口已更新，包含主要UI组件导出
# }}

"""
用户界面层 (UI Layer)
负责所有用户交互界面组件
"""

# 导出主要UI组件
from .main_window import MainWindow, create_application
from .canvas_widget import CanvasWidget, CanvasScene
from .toolbar import BlockToolbar, BlockItem, BlockCategoryItem
from .property_editor import PropertyEditor
from .log_viewer import LogViewer, LogLevel, LogEntry

__all__ = [
    # 主窗口
    'MainWindow',
    'create_application',

    # 画布组件
    'CanvasWidget',
    'CanvasScene',

    # 工具栏组件
    'BlockToolbar',
    'BlockItem',
    'BlockCategoryItem',

    # 属性编辑器
    'PropertyEditor',

    # 日志查看器
    'LogViewer',
    'LogLevel',
    'LogEntry'
]
