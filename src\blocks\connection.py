# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 11:45:00 +08:00]
# Reason: P1-ARCH-002 - 实现积木连接系统，支持积木之间的数据流连接
# Principle_Applied: 单一职责原则 - Connection类只负责连接管理，接口隔离原则 - 连接接口简洁
# Optimization: 连接验证机制，防止无效连接，支持连接状态管理
# Architectural_Note (AR): 积木连接系统，支持数据流管理和连接验证
# Documentation_Note (DW): 积木连接系统已实现，包含连接验证和状态管理
# }}

"""
积木连接系统
管理积木之间的连接关系和数据流
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from .base import Port


class ConnectionStatus(Enum):
    """连接状态枚举"""
    VALID = "valid"
    INVALID = "invalid"
    DISCONNECTED = "disconnected"


@dataclass
class ConnectionPoint:
    """
    连接点
    表示积木的一个输入或输出端口
    """
    block_id: str
    port_name: str
    port_type: str  # "input" or "output"
    
    def __str__(self) -> str:
        return f"{self.block_id}.{self.port_name}({self.port_type})"


@dataclass
class Connection:
    """
    积木连接
    表示两个积木端口之间的连接
    """
    connection_id: str
    source: ConnectionPoint  # 输出端口
    target: ConnectionPoint  # 输入端口
    status: ConnectionStatus = ConnectionStatus.VALID
    
    def __str__(self) -> str:
        return f"{self.source} -> {self.target}"

    def is_valid(self) -> bool:
        """检查连接是否有效"""
        return self.status == ConnectionStatus.VALID

    def get_source_block_id(self) -> str:
        """获取源积木ID"""
        return self.source.block_id

    def get_target_block_id(self) -> str:
        """获取目标积木ID"""
        return self.target.block_id


class ConnectionManager:
    """
    连接管理器
    管理所有积木之间的连接关系
    """
    
    def __init__(self):
        self._connections: Dict[str, Connection] = {}
        self._block_ports: Dict[str, Dict[str, Port]] = {}  # block_id -> {port_name: Port}
        self._next_connection_id = 1

    def register_block_ports(self, block_id: str, input_ports: List[Port], output_ports: List[Port]) -> None:
        """
        注册积木的端口信息
        
        Args:
            block_id: 积木ID
            input_ports: 输入端口列表
            output_ports: 输出端口列表
        """
        if block_id not in self._block_ports:
            self._block_ports[block_id] = {}
        
        # 注册输入端口
        for port in input_ports:
            self._block_ports[block_id][f"input_{port.name}"] = port
        
        # 注册输出端口
        for port in output_ports:
            self._block_ports[block_id][f"output_{port.name}"] = port

    def create_connection(self, source_block_id: str, source_port: str, 
                         target_block_id: str, target_port: str) -> Optional[str]:
        """
        创建连接
        
        Args:
            source_block_id: 源积木ID
            source_port: 源端口名称
            target_block_id: 目标积木ID
            target_port: 目标端口名称
            
        Returns:
            Optional[str]: 连接ID，如果创建失败返回None
        """
        # 验证连接有效性
        if not self._validate_connection(source_block_id, source_port, target_block_id, target_port):
            return None
        
        # 检查目标端口是否已有连接
        existing_connection = self._find_connection_to_input(target_block_id, target_port)
        if existing_connection:
            # 删除现有连接
            self.remove_connection(existing_connection.connection_id)
        
        # 创建新连接
        connection_id = f"conn_{self._next_connection_id}"
        self._next_connection_id += 1
        
        source_point = ConnectionPoint(source_block_id, source_port, "output")
        target_point = ConnectionPoint(target_block_id, target_port, "input")
        
        connection = Connection(connection_id, source_point, target_point)
        self._connections[connection_id] = connection
        
        return connection_id

    def remove_connection(self, connection_id: str) -> bool:
        """
        删除连接
        
        Args:
            connection_id: 连接ID
            
        Returns:
            bool: 是否删除成功
        """
        if connection_id in self._connections:
            del self._connections[connection_id]
            return True
        return False

    def get_connection(self, connection_id: str) -> Optional[Connection]:
        """
        获取连接
        
        Args:
            connection_id: 连接ID
            
        Returns:
            Optional[Connection]: 连接对象
        """
        return self._connections.get(connection_id)

    def get_all_connections(self) -> List[Connection]:
        """
        获取所有连接
        
        Returns:
            List[Connection]: 连接列表
        """
        return list(self._connections.values())

    def get_block_connections(self, block_id: str) -> List[Connection]:
        """
        获取指定积木的所有连接
        
        Args:
            block_id: 积木ID
            
        Returns:
            List[Connection]: 连接列表
        """
        connections = []
        for connection in self._connections.values():
            if (connection.source.block_id == block_id or 
                connection.target.block_id == block_id):
                connections.append(connection)
        return connections

    def get_input_connections(self, block_id: str) -> List[Connection]:
        """
        获取指定积木的输入连接
        
        Args:
            block_id: 积木ID
            
        Returns:
            List[Connection]: 输入连接列表
        """
        connections = []
        for connection in self._connections.values():
            if connection.target.block_id == block_id:
                connections.append(connection)
        return connections

    def get_output_connections(self, block_id: str) -> List[Connection]:
        """
        获取指定积木的输出连接
        
        Args:
            block_id: 积木ID
            
        Returns:
            List[Connection]: 输出连接列表
        """
        connections = []
        for connection in self._connections.values():
            if connection.source.block_id == block_id:
                connections.append(connection)
        return connections

    def validate_all_connections(self) -> List[str]:
        """
        验证所有连接的有效性
        
        Returns:
            List[str]: 无效连接的ID列表
        """
        invalid_connections = []
        for connection in self._connections.values():
            if not self._validate_existing_connection(connection):
                connection.status = ConnectionStatus.INVALID
                invalid_connections.append(connection.connection_id)
            else:
                connection.status = ConnectionStatus.VALID
        return invalid_connections

    def remove_block_connections(self, block_id: str) -> int:
        """
        删除指定积木的所有连接
        
        Args:
            block_id: 积木ID
            
        Returns:
            int: 删除的连接数量
        """
        connections_to_remove = []
        for connection_id, connection in self._connections.items():
            if (connection.source.block_id == block_id or 
                connection.target.block_id == block_id):
                connections_to_remove.append(connection_id)
        
        for connection_id in connections_to_remove:
            del self._connections[connection_id]
        
        # 同时删除端口注册信息
        if block_id in self._block_ports:
            del self._block_ports[block_id]
        
        return len(connections_to_remove)

    def _validate_connection(self, source_block_id: str, source_port: str,
                           target_block_id: str, target_port: str) -> bool:
        """验证连接是否有效"""
        # 不能连接到自己
        if source_block_id == target_block_id:
            return False
        
        # 检查端口是否存在
        source_key = f"output_{source_port}"
        target_key = f"input_{target_port}"
        
        if (source_block_id not in self._block_ports or
            source_key not in self._block_ports[source_block_id]):
            return False
        
        if (target_block_id not in self._block_ports or
            target_key not in self._block_ports[target_block_id]):
            return False
        
        # 检查端口类型兼容性
        source_port_obj = self._block_ports[source_block_id][source_key]
        target_port_obj = self._block_ports[target_block_id][target_key]
        
        return source_port_obj.is_compatible(target_port_obj)

    def _validate_existing_connection(self, connection: Connection) -> bool:
        """验证现有连接是否仍然有效"""
        return self._validate_connection(
            connection.source.block_id, connection.source.port_name,
            connection.target.block_id, connection.target.port_name
        )

    def _find_connection_to_input(self, block_id: str, port_name: str) -> Optional[Connection]:
        """查找连接到指定输入端口的连接"""
        for connection in self._connections.values():
            if (connection.target.block_id == block_id and 
                connection.target.port_name == port_name):
                return connection
        return None
