# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 11:45:00 +08:00]
# Reason: P1-ARCH-002 - 实现积木注册系统，支持积木的动态注册和发现
# Principle_Applied: 单一职责原则 - 注册器只负责积木管理，开闭原则 - 支持新积木类型注册
# Optimization: 使用单例模式确保全局唯一注册器，类型安全的积木管理
# Architectural_Note (AR): 积木注册系统，支持插件化扩展
# Documentation_Note (DW): 积木注册器已实现，支持动态注册和分类管理
# }}

"""
积木注册系统
负责积木类型的注册、发现和管理
"""

from typing import Dict, List, Type, Optional
from .base import IBlock


class BlockRegistry:
    """
    积木注册器
    使用单例模式确保全局唯一
    """
    
    _instance: Optional['BlockRegistry'] = None
    _initialized: bool = False

    def __new__(cls) -> 'BlockRegistry':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._blocks: Dict[str, Type[IBlock]] = {}
            self._categories: Dict[str, List[str]] = {}
            self._initialized = True

    def register_block(self, block_class: Type[IBlock]) -> bool:
        """
        注册积木类型
        
        Args:
            block_class: 积木类
            
        Returns:
            bool: 注册是否成功
        """
        try:
            # 创建临时实例以获取元数据
            temp_instance = block_class()
            block_name = temp_instance.get_name()
            category = temp_instance.get_category()
            
            if not block_name:
                raise ValueError("积木名称不能为空")
            
            if block_name in self._blocks:
                raise ValueError(f"积木 '{block_name}' 已存在")
            
            # 注册积木
            self._blocks[block_name] = block_class
            
            # 更新分类
            if category not in self._categories:
                self._categories[category] = []
            
            if block_name not in self._categories[category]:
                self._categories[category].append(block_name)
            
            return True
            
        except Exception as e:
            print(f"注册积木失败: {e}")
            return False

    def unregister_block(self, block_name: str) -> bool:
        """
        注销积木类型
        
        Args:
            block_name: 积木名称
            
        Returns:
            bool: 注销是否成功
        """
        if block_name not in self._blocks:
            return False
        
        # 获取分类信息
        block_class = self._blocks[block_name]
        temp_instance = block_class()
        category = temp_instance.get_category()
        
        # 从注册表中移除
        del self._blocks[block_name]
        
        # 从分类中移除
        if category in self._categories:
            if block_name in self._categories[category]:
                self._categories[category].remove(block_name)
            
            # 如果分类为空，删除分类
            if not self._categories[category]:
                del self._categories[category]
        
        return True

    def get_block_class(self, block_name: str) -> Optional[Type[IBlock]]:
        """
        获取积木类
        
        Args:
            block_name: 积木名称
            
        Returns:
            Optional[Type[IBlock]]: 积木类，如果不存在返回None
        """
        return self._blocks.get(block_name)

    def create_block(self, block_name: str, block_id: str = "") -> Optional[IBlock]:
        """
        创建积木实例
        
        Args:
            block_name: 积木名称
            block_id: 积木ID
            
        Returns:
            Optional[IBlock]: 积木实例，如果创建失败返回None
        """
        block_class = self.get_block_class(block_name)
        if block_class is None:
            return None
        
        try:
            return block_class(block_id)
        except Exception as e:
            print(f"创建积木实例失败: {e}")
            return None

    def get_all_blocks(self) -> List[str]:
        """
        获取所有已注册的积木名称
        
        Returns:
            List[str]: 积木名称列表
        """
        return list(self._blocks.keys())

    def get_blocks_by_category(self, category: str) -> List[str]:
        """
        根据分类获取积木列表
        
        Args:
            category: 分类名称
            
        Returns:
            List[str]: 积木名称列表
        """
        return self._categories.get(category, []).copy()

    def get_all_categories(self) -> List[str]:
        """
        获取所有分类
        
        Returns:
            List[str]: 分类列表
        """
        return list(self._categories.keys())

    def get_block_info(self, block_name: str) -> Optional[Dict[str, str]]:
        """
        获取积木信息
        
        Args:
            block_name: 积木名称
            
        Returns:
            Optional[Dict[str, str]]: 积木信息，包含名称、描述、分类
        """
        block_class = self.get_block_class(block_name)
        if block_class is None:
            return None
        
        try:
            temp_instance = block_class()
            return {
                "name": temp_instance.get_name(),
                "description": temp_instance.get_description(),
                "category": temp_instance.get_category()
            }
        except Exception:
            return None

    def is_registered(self, block_name: str) -> bool:
        """
        检查积木是否已注册
        
        Args:
            block_name: 积木名称
            
        Returns:
            bool: 是否已注册
        """
        return block_name in self._blocks

    def clear_registry(self) -> None:
        """
        清空注册表
        主要用于测试
        """
        self._blocks.clear()
        self._categories.clear()

    def get_registry_stats(self) -> Dict[str, int]:
        """
        获取注册表统计信息
        
        Returns:
            Dict[str, int]: 统计信息
        """
        return {
            "total_blocks": len(self._blocks),
            "total_categories": len(self._categories),
            "blocks_per_category": {
                category: len(blocks) 
                for category, blocks in self._categories.items()
            }
        }


# 全局注册器实例
block_registry = BlockRegistry()


def register_block(block_class: Type[IBlock]) -> bool:
    """
    注册积木的便捷函数
    
    Args:
        block_class: 积木类
        
    Returns:
        bool: 注册是否成功
    """
    return block_registry.register_block(block_class)


def get_block_registry() -> BlockRegistry:
    """
    获取全局积木注册器
    
    Returns:
        BlockRegistry: 积木注册器实例
    """
    return block_registry
