# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 12:00:00 +08:00]
# Reason: P1-GUI-003 - 创建简化GUI测试验证PyQt界面功能
# Principle_Applied: KISS原则 - 简化测试逻辑，快速验证GUI功能
# Optimization: 直接验证GUI组件导入和基础功能，避免复杂测试
# Architectural_Note (AR): 简化验证GUI系统实现正确性
# Documentation_Note (DW): 简化GUI测试脚本，用于快速验证界面系统
# }}

"""
GUI系统简化测试
快速验证PyQt界面系统基础功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_pyqt_import():
    """测试PyQt导入"""
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon
        print("✓ PyQt5导入成功")
        return True
    except ImportError as e:
        print(f"✗ PyQt5导入失败: {e}")
        print("请安装PyQt5: pip install PyQt5")
        return False

def test_ui_imports():
    """测试UI组件导入"""
    try:
        from ui import (
            MainWindow, create_application, CanvasWidget, 
            BlockToolbar, PropertyEditor, LogViewer
        )
        print("✓ UI组件导入成功")
        return True
    except Exception as e:
        print(f"✗ UI组件导入失败: {e}")
        return False

def test_main_window_creation():
    """测试主窗口创建"""
    try:
        from ui import MainWindow, create_application
        
        # 创建应用程序（不显示）
        app = create_application()
        
        # 创建主窗口
        window = MainWindow()
        
        # 检查窗口属性
        assert window.windowTitle() == "内网RPA工具 - 积木式可视化自动化工具"
        assert window.minimumSize().width() == 1200
        assert window.minimumSize().height() == 800
        
        # 检查组件是否存在
        assert hasattr(window, 'canvas_widget')
        assert hasattr(window, 'block_toolbar')
        assert hasattr(window, 'property_editor')
        assert hasattr(window, 'log_viewer')
        
        print("✓ 主窗口创建测试通过")
        return True
    except Exception as e:
        print(f"✗ 主窗口创建测试失败: {e}")
        return False

def test_canvas_widget():
    """测试画布组件"""
    try:
        from ui import CanvasWidget
        from PyQt5.QtCore import QPointF
        
        canvas = CanvasWidget()
        
        # 测试基本功能
        assert canvas.get_log_count is not None  # 方法存在检查
        
        # 测试添加积木（模拟）
        block_id = canvas.add_block("测试积木", QPointF(100, 100))
        assert block_id is not None
        
        # 测试获取积木
        blocks = canvas.get_blocks()
        assert len(blocks) == 1
        
        print("✓ 画布组件测试通过")
        return True
    except Exception as e:
        print(f"✗ 画布组件测试失败: {e}")
        return False

def test_block_toolbar():
    """测试积木工具栏"""
    try:
        from ui import BlockToolbar
        
        toolbar = BlockToolbar()
        
        # 检查默认积木是否加载
        categories = toolbar.get_all_categories()
        assert len(categories) > 0
        assert "基础操作" in categories
        
        # 检查积木信息
        click_info = toolbar.get_block_info("点击积木")
        assert click_info is not None
        assert click_info["name"] == "点击积木"
        
        print("✓ 积木工具栏测试通过")
        return True
    except Exception as e:
        print(f"✗ 积木工具栏测试失败: {e}")
        return False

def test_property_editor():
    """测试属性编辑器"""
    try:
        from ui import PropertyEditor
        
        editor = PropertyEditor()
        
        # 测试无选择状态
        editor.set_block(None)
        
        # 测试设置积木数据
        block_data = {
            'id': 'test_block',
            'type': '点击积木',
            'position': None
        }
        editor.set_block(block_data)
        
        print("✓ 属性编辑器测试通过")
        return True
    except Exception as e:
        print(f"✗ 属性编辑器测试失败: {e}")
        return False

def test_log_viewer():
    """测试日志查看器"""
    try:
        from ui import LogViewer, LogLevel
        
        viewer = LogViewer()
        
        # 测试添加日志
        viewer.add_info("测试信息日志")
        viewer.add_warning("测试警告日志")
        viewer.add_error("测试错误日志")
        
        # 检查日志数量
        assert viewer.get_log_count() >= 3  # 包括默认示例日志
        
        # 测试按级别获取日志
        error_logs = viewer.get_logs_by_level(LogLevel.ERROR)
        assert len(error_logs) >= 1
        
        print("✓ 日志查看器测试通过")
        return True
    except Exception as e:
        print(f"✗ 日志查看器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("GUI系统基础功能测试")
    print("=" * 50)
    
    tests = [
        test_pyqt_import,
        test_ui_imports,
        test_main_window_creation,
        test_canvas_widget,
        test_block_toolbar,
        test_property_editor,
        test_log_viewer
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！GUI系统基础架构实现正确")
        return True
    else:
        print("✗ 部分测试失败，需要检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
