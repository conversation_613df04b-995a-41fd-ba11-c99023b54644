# {{CHENGQI:
# Action: Modified
# Timestamp: [2025-01-27 11:45:00 +08:00]
# Reason: P1-ARCH-002 - 更新积木包导出，提供核心接口访问
# Principle_Applied: 接口隔离原则 - 只导出必要的公共接口，KISS原则 - 简化导入路径
# Optimization: 统一的包接口，便于外部模块使用
# Architectural_Note (AR): 积木系统公共接口，符合模块化设计
# Documentation_Note (DW): 积木包接口已更新，包含核心类和函数导出
# }}

"""
积木系统 (Block System)
包含所有积木类型和积木基础接口
"""

# 导出核心接口
from .base import (
    IBlock,
    BaseBlock,
    Port,
    DataType,
    ExecutionContext,
    ExecutionResult
)

from .registry import (
    BlockRegistry,
    block_registry,
    register_block,
    get_block_registry
)

from .connection import (
    Connection,
    ConnectionPoint,
    ConnectionManager,
    ConnectionStatus
)

__all__ = [
    # 基础接口
    'IBlock',
    'BaseBlock',
    'Port',
    'DataType',
    'ExecutionContext',
    'ExecutionResult',

    # 注册系统
    'BlockRegistry',
    'block_registry',
    'register_block',
    'get_block_registry',

    # 连接系统
    'Connection',
    'ConnectionPoint',
    'ConnectionManager',
    'ConnectionStatus'
]
