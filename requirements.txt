# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 11:30:00 +08:00]
# Reason: P1-SETUP-001 - 创建项目依赖配置文件，固定版本号避免冲突
# Principle_Applied: KISS原则 - 保持依赖列表简洁明确，DRY原则 - 统一依赖管理
# Optimization: 选择稳定版本，避免beta版本
# Architectural_Note (AR): 依赖选择符合架构设计中的技术栈要求
# Documentation_Note (DW): 项目依赖文档已创建，版本固定确保环境一致性
# }}

# 内网RPA工具 - 项目依赖
# 项目：积木式可视化RPA工具
# Python版本要求：*********

# GUI框架 - PyQt5 (稳定版本)
PyQt5==5.15.10
PyQt5-Qt5==5.15.2
PyQt5-sip==12.13.0

# 网页自动化核心
selenium==4.15.2
webdriver-manager==4.0.1

# Office文档处理
python-docx==1.1.0
openpyxl==3.1.2
python-pptx==0.6.23

# 数据处理
pandas==2.1.4
requests==2.31.0

# 配置文件处理
PyYAML==6.0.1

# 日志和工具
loguru==0.7.2

# 加密支持
cryptography==41.0.8

# 打包工具
PyInstaller==6.3.0

# 开发和测试工具
pytest==7.4.3
pytest-qt==4.2.0
pytest-cov==4.1.0

# 代码质量
black==23.12.1
flake8==6.1.0
mypy==1.8.0

# 类型注解支持
typing-extensions==4.9.0
