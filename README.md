# 内网RPA工具 - 积木式可视化自动化工具

## 项目简介

一款专为小白用户设计的内网环境RPA工具，采用创新的积木式可视化设计，让用户像搭积木一样简单地创建自动化流程。

### 核心特色

- 🧩 **积木式设计**：拖拽积木块创建自动化流程，零代码操作
- 🌐 **网页自动化**：支持Chrome浏览器的数据抓取和流程操作
- 📄 **Office自动化**：支持Word、Excel、PowerPoint文档处理
- 💻 **内网优化**：专为企业内网环境设计，无外部依赖
- 📦 **单文件部署**：打包为单个exe文件，零配置运行
- 🔧 **配置管理**：支持流程配置的导入导出和模板库

### 技术架构

- **GUI框架**：PyQt5
- **自动化引擎**：Selenium WebDriver
- **Office处理**：python-docx, openpyxl, python-pptx
- **配置格式**：JSON
- **打包工具**：PyInstaller

## 开发环境设置

### 环境要求

- Python *********
- Windows 10/11
- Chrome浏览器

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd rpa-tool
```

2. 创建虚拟环境
```bash
python -m venv venv
venv\Scripts\activate
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 运行程序
```bash
python main.py
```

## 项目结构

```
rpa-tool/
├── src/                    # 源代码目录
│   ├── ui/                 # 用户界面层
│   ├── core/               # 业务逻辑层
│   ├── automation/         # 自动化执行层
│   ├── data/               # 数据访问层
│   └── blocks/             # 积木系统
├── project_document/       # 项目文档
├── tests/                  # 测试文件
├── resources/              # 资源文件
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖配置
└── README.md              # 项目说明
```

## 开发指南

### 代码规范

- 遵循PEP 8代码风格
- 使用类型注解
- 编写完整的文档字符串
- 遵循SOLID设计原则

### 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=src
```

### 代码质量检查

```bash
# 代码格式化
black src/

# 代码风格检查
flake8 src/

# 类型检查
mypy src/
```

## 构建和打包

```bash
# 使用PyInstaller打包为单个exe文件
pyinstaller --onefile --windowed main.py
```

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request。请确保：

1. 代码符合项目规范
2. 包含适当的测试
3. 更新相关文档

## 联系方式

- 项目维护者：RPA Development Team
- 技术支持：请提交Issue

---

**注意**：本工具专为企业内网环境设计，请确保在合规的环境中使用。
