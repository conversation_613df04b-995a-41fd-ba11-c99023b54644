# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 12:00:00 +08:00]
# Reason: P1-GUI-003 - 创建属性编辑器，支持积木属性配置
# Principle_Applied: 单一职责原则 - 属性编辑器只负责属性编辑，接口隔离原则 - 编辑器接口简洁
# Optimization: 动态生成编辑界面，支持多种数据类型
# Architectural_Note (AR): 属性编辑器组件，支持积木配置管理
# Documentation_Note (DW): 属性编辑器已实现，包含动态界面生成功能
# }}

"""
属性编辑器
用于编辑选中积木的属性配置
"""

from typing import Any, Dict, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QTextEdit, QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox,
    QGroupBox, QFormLayout, QScrollArea, QPushButton
)
from PyQt5.QtCore import Qt, pyqtSignal


class PropertyEditor(QWidget):
    """
    属性编辑器
    动态生成积木属性的编辑界面
    """
    
    # 信号定义
    property_changed = pyqtSignal(str, str, object)  # 属性变化信号 (block_id, property_name, value)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._current_block: Optional[Any] = None
        self._property_widgets: Dict[str, QWidget] = {}
        self._setup_ui()
        
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        title_label = QLabel("属性编辑器")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                background-color: #e0e0e0;
                border-radius: 3px;
            }
        """)
        layout.addWidget(title_label)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 属性容器
        self.property_container = QWidget()
        self.property_layout = QVBoxLayout(self.property_container)
        self.property_layout.setContentsMargins(5, 5, 5, 5)
        
        scroll_area.setWidget(self.property_container)
        layout.addWidget(scroll_area)
        
        # 默认显示
        self._show_no_selection()
        
    def _show_no_selection(self) -> None:
        """显示无选择状态"""
        self._clear_properties()
        
        no_selection_label = QLabel("请选择一个积木来编辑其属性")
        no_selection_label.setAlignment(Qt.AlignCenter)
        no_selection_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-style: italic;
                padding: 20px;
            }
        """)
        self.property_layout.addWidget(no_selection_label)
        
    def _clear_properties(self) -> None:
        """清空属性编辑器"""
        # 清除所有子组件
        while self.property_layout.count():
            child = self.property_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
        self._property_widgets.clear()
        
    def set_block(self, block_data: Optional[Any]) -> None:
        """
        设置要编辑的积木
        
        Args:
            block_data: 积木数据，None表示无选择
        """
        self._current_block = block_data
        
        if block_data is None:
            self._show_no_selection()
            return
            
        self._create_property_interface(block_data)
        
    def _create_property_interface(self, block_data: Any) -> None:
        """
        创建属性编辑界面
        
        Args:
            block_data: 积木数据
        """
        self._clear_properties()
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        # 积木ID（只读）
        id_edit = QLineEdit(block_data.get('id', ''))
        id_edit.setReadOnly(True)
        basic_layout.addRow("积木ID:", id_edit)
        
        # 积木类型（只读）
        type_edit = QLineEdit(block_data.get('type', ''))
        type_edit.setReadOnly(True)
        basic_layout.addRow("积木类型:", type_edit)
        
        # 位置信息
        position = block_data.get('position')
        if position:
            pos_edit = QLineEdit(f"({position.x():.0f}, {position.y():.0f})")
            pos_edit.setReadOnly(True)
            basic_layout.addRow("位置:", pos_edit)
            
        self.property_layout.addWidget(basic_group)
        
        # 配置属性组
        config_group = QGroupBox("配置属性")
        config_layout = QFormLayout(config_group)
        
        # TODO: 根据积木类型动态生成配置界面
        # 这里先添加一些通用配置项
        self._add_common_properties(config_layout, block_data)
        
        self.property_layout.addWidget(config_group)
        
        # 添加弹性空间
        self.property_layout.addStretch()
        
    def _add_common_properties(self, layout: QFormLayout, block_data: Any) -> None:
        """
        添加通用属性
        
        Args:
            layout: 表单布局
            block_data: 积木数据
        """
        block_type = block_data.get('type', '')
        
        if block_type == "点击积木":
            self._add_click_properties(layout, block_data)
        elif block_type == "填写积木":
            self._add_input_properties(layout, block_data)
        elif block_type == "等待积木":
            self._add_wait_properties(layout, block_data)
        elif block_type == "提取积木":
            self._add_extract_properties(layout, block_data)
        else:
            # 默认属性
            self._add_default_properties(layout, block_data)
            
    def _add_click_properties(self, layout: QFormLayout, block_data: Any) -> None:
        """添加点击积木属性"""
        # 元素定位器
        locator_edit = QLineEdit()
        locator_edit.setPlaceholderText("CSS选择器或XPath")
        locator_edit.textChanged.connect(
            lambda text: self._on_property_changed('locator', text)
        )
        layout.addRow("元素定位器:", locator_edit)
        self._property_widgets['locator'] = locator_edit
        
        # 等待时间
        wait_spin = QDoubleSpinBox()
        wait_spin.setRange(0, 60)
        wait_spin.setValue(2.0)
        wait_spin.setSuffix(" 秒")
        wait_spin.valueChanged.connect(
            lambda value: self._on_property_changed('wait_time', value)
        )
        layout.addRow("等待时间:", wait_spin)
        self._property_widgets['wait_time'] = wait_spin
        
    def _add_input_properties(self, layout: QFormLayout, block_data: Any) -> None:
        """添加填写积木属性"""
        # 元素定位器
        locator_edit = QLineEdit()
        locator_edit.setPlaceholderText("CSS选择器或XPath")
        locator_edit.textChanged.connect(
            lambda text: self._on_property_changed('locator', text)
        )
        layout.addRow("元素定位器:", locator_edit)
        self._property_widgets['locator'] = locator_edit
        
        # 输入内容
        content_edit = QLineEdit()
        content_edit.setPlaceholderText("要输入的内容")
        content_edit.textChanged.connect(
            lambda text: self._on_property_changed('content', text)
        )
        layout.addRow("输入内容:", content_edit)
        self._property_widgets['content'] = content_edit
        
        # 清空输入框
        clear_check = QCheckBox()
        clear_check.setChecked(True)
        clear_check.toggled.connect(
            lambda checked: self._on_property_changed('clear_first', checked)
        )
        layout.addRow("先清空输入框:", clear_check)
        self._property_widgets['clear_first'] = clear_check
        
    def _add_wait_properties(self, layout: QFormLayout, block_data: Any) -> None:
        """添加等待积木属性"""
        # 等待时间
        wait_spin = QDoubleSpinBox()
        wait_spin.setRange(0.1, 300)
        wait_spin.setValue(3.0)
        wait_spin.setSuffix(" 秒")
        wait_spin.valueChanged.connect(
            lambda value: self._on_property_changed('wait_time', value)
        )
        layout.addRow("等待时间:", wait_spin)
        self._property_widgets['wait_time'] = wait_spin
        
    def _add_extract_properties(self, layout: QFormLayout, block_data: Any) -> None:
        """添加提取积木属性"""
        # 元素定位器
        locator_edit = QLineEdit()
        locator_edit.setPlaceholderText("CSS选择器或XPath")
        locator_edit.textChanged.connect(
            lambda text: self._on_property_changed('locator', text)
        )
        layout.addRow("元素定位器:", locator_edit)
        self._property_widgets['locator'] = locator_edit
        
        # 提取属性
        attribute_combo = QComboBox()
        attribute_combo.addItems(["text", "value", "href", "src", "title"])
        attribute_combo.setEditable(True)
        attribute_combo.currentTextChanged.connect(
            lambda text: self._on_property_changed('attribute', text)
        )
        layout.addRow("提取属性:", attribute_combo)
        self._property_widgets['attribute'] = attribute_combo
        
        # 变量名
        variable_edit = QLineEdit()
        variable_edit.setPlaceholderText("保存到变量")
        variable_edit.textChanged.connect(
            lambda text: self._on_property_changed('variable_name', text)
        )
        layout.addRow("变量名:", variable_edit)
        self._property_widgets['variable_name'] = variable_edit
        
    def _add_default_properties(self, layout: QFormLayout, block_data: Any) -> None:
        """添加默认属性"""
        # 描述
        description_edit = QTextEdit()
        description_edit.setMaximumHeight(60)
        description_edit.setPlaceholderText("积木描述...")
        description_edit.textChanged.connect(
            lambda: self._on_property_changed('description', description_edit.toPlainText())
        )
        layout.addRow("描述:", description_edit)
        self._property_widgets['description'] = description_edit
        
    def _on_property_changed(self, property_name: str, value: Any) -> None:
        """
        处理属性变化
        
        Args:
            property_name: 属性名称
            value: 属性值
        """
        if self._current_block:
            block_id = self._current_block.get('id', '')
            self.property_changed.emit(block_id, property_name, value)
            
    def get_property_values(self) -> Dict[str, Any]:
        """
        获取所有属性值
        
        Returns:
            Dict[str, Any]: 属性值字典
        """
        values = {}
        
        for name, widget in self._property_widgets.items():
            if isinstance(widget, QLineEdit):
                values[name] = widget.text()
            elif isinstance(widget, QTextEdit):
                values[name] = widget.toPlainText()
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                values[name] = widget.value()
            elif isinstance(widget, QCheckBox):
                values[name] = widget.isChecked()
            elif isinstance(widget, QComboBox):
                values[name] = widget.currentText()
                
        return values
        
    def set_property_values(self, values: Dict[str, Any]) -> None:
        """
        设置属性值
        
        Args:
            values: 属性值字典
        """
        for name, value in values.items():
            if name in self._property_widgets:
                widget = self._property_widgets[name]
                
                if isinstance(widget, QLineEdit):
                    widget.setText(str(value))
                elif isinstance(widget, QTextEdit):
                    widget.setPlainText(str(value))
                elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                    widget.setValue(float(value))
                elif isinstance(widget, QCheckBox):
                    widget.setChecked(bool(value))
                elif isinstance(widget, QComboBox):
                    widget.setCurrentText(str(value))
