# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 11:30:00 +08:00]
# Reason: P1-SETUP-001 - 创建应用程序主入口文件
# Principle_Applied: KISS原则 - 主入口简洁明确，单一职责原则 - 只负责应用启动
# Optimization: 简化启动逻辑，便于调试和维护
# Architectural_Note (AR): 应用程序入口点，符合标准Python应用结构
# Documentation_Note (DW): 主程序入口已创建，包含基础启动逻辑
# }}

"""
内网RPA工具 - 积木式可视化自动化工具
主程序入口文件

运行方式：
python main.py
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


def main():
    """
    主程序入口函数
    """
    try:
        # 导入UI组件
        from ui import MainWindow, create_application

        print("内网RPA工具 - 积木式可视化自动化工具")
        print("版本：1.0.0")
        print("正在启动GUI界面...")

        # 创建Qt应用程序
        app = create_application()

        # 创建主窗口
        window = MainWindow()
        window.show()

        print("GUI界面启动成功！")

        # 运行应用程序
        sys.exit(app.exec_())

    except ImportError as e:
        print(f"导入错误：{e}")
        print("请确保已安装PyQt5：pip install PyQt5")
        sys.exit(1)
    except Exception as e:
        print(f"程序启动失败：{e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
