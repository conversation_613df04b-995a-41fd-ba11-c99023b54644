# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 12:00:00 +08:00]
# Reason: P1-GUI-003 - 创建日志查看器，显示执行日志和状态信息
# Principle_Applied: 单一职责原则 - 日志查看器只负责日志显示，接口隔离原则 - 查看器接口简洁
# Optimization: 支持日志过滤和自动滚动，提供良好的用户体验
# Architectural_Note (AR): 日志查看器组件，支持执行状态监控
# Documentation_Note (DW): 日志查看器已实现，包含日志过滤和显示功能
# }}

"""
日志查看器
显示执行日志和状态信息
"""

from typing import List, Optional
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTextEdit, 
    QPushButton, QComboBox, QCheckBox, QLineEdit
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QTextCursor, QColor, QTextCharFormat


class LogLevel:
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    SUCCESS = "SUCCESS"


class LogEntry:
    """日志条目"""
    
    def __init__(self, level: str, message: str, timestamp: Optional[datetime] = None):
        self.level = level
        self.message = message
        self.timestamp = timestamp or datetime.now()
        
    def __str__(self) -> str:
        time_str = self.timestamp.strftime("%H:%M:%S")
        return f"[{time_str}] [{self.level}] {self.message}"


class LogViewer(QWidget):
    """
    日志查看器
    显示和管理执行日志
    """
    
    # 信号定义
    log_cleared = pyqtSignal()  # 日志清空信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._logs: List[LogEntry] = []
        self._auto_scroll = True
        self._max_logs = 1000  # 最大日志条数
        self._setup_ui()
        
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题和控制栏
        header_layout = QHBoxLayout()
        
        title_label = QLabel("执行日志")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                background-color: #e0e0e0;
                border-radius: 3px;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 日志级别过滤
        level_combo = QComboBox()
        level_combo.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "SUCCESS"])
        level_combo.currentTextChanged.connect(self._filter_logs)
        header_layout.addWidget(level_combo)
        self.level_filter = level_combo
        
        layout.addLayout(header_layout)
        
        # 搜索栏
        search_layout = QHBoxLayout()
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索日志...")
        self.search_edit.textChanged.connect(self._filter_logs)
        search_layout.addWidget(self.search_edit)
        
        layout.addLayout(search_layout)
        
        # 日志显示区域
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setFont(self._get_monospace_font())
        
        # 设置样式
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f8f8;
                border: 1px solid #cccccc;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 9pt;
            }
        """)
        
        layout.addWidget(self.log_display)
        
        # 控制按钮栏
        control_layout = QHBoxLayout()
        
        # 自动滚动复选框
        auto_scroll_check = QCheckBox("自动滚动")
        auto_scroll_check.setChecked(self._auto_scroll)
        auto_scroll_check.toggled.connect(self._set_auto_scroll)
        control_layout.addWidget(auto_scroll_check)
        
        control_layout.addStretch()
        
        # 清空日志按钮
        clear_button = QPushButton("清空日志")
        clear_button.clicked.connect(self.clear_logs)
        control_layout.addWidget(clear_button)
        
        # 导出日志按钮
        export_button = QPushButton("导出日志")
        export_button.clicked.connect(self._export_logs)
        control_layout.addWidget(export_button)
        
        layout.addLayout(control_layout)
        
        # 添加一些示例日志
        self._add_sample_logs()
        
    def _get_monospace_font(self):
        """获取等宽字体"""
        from PyQt5.QtGui import QFont
        font = QFont()
        font.setFamily("Consolas")
        if not font.exactMatch():
            font.setFamily("Monaco")
        if not font.exactMatch():
            font.setFamily("Courier New")
        font.setPointSize(9)
        return font
        
    def _add_sample_logs(self) -> None:
        """添加示例日志"""
        self.add_log(LogLevel.INFO, "应用程序启动")
        self.add_log(LogLevel.INFO, "积木系统初始化完成")
        self.add_log(LogLevel.SUCCESS, "准备就绪，等待用户操作")
        
    def add_log(self, level: str, message: str, timestamp: Optional[datetime] = None) -> None:
        """
        添加日志条目
        
        Args:
            level: 日志级别
            message: 日志消息
            timestamp: 时间戳，None表示使用当前时间
        """
        log_entry = LogEntry(level, message, timestamp)
        self._logs.append(log_entry)
        
        # 限制日志数量
        if len(self._logs) > self._max_logs:
            self._logs = self._logs[-self._max_logs:]
            
        self._update_display()
        
    def add_debug(self, message: str) -> None:
        """添加调试日志"""
        self.add_log(LogLevel.DEBUG, message)
        
    def add_info(self, message: str) -> None:
        """添加信息日志"""
        self.add_log(LogLevel.INFO, message)
        
    def add_warning(self, message: str) -> None:
        """添加警告日志"""
        self.add_log(LogLevel.WARNING, message)
        
    def add_error(self, message: str) -> None:
        """添加错误日志"""
        self.add_log(LogLevel.ERROR, message)
        
    def add_success(self, message: str) -> None:
        """添加成功日志"""
        self.add_log(LogLevel.SUCCESS, message)
        
    def clear_logs(self) -> None:
        """清空所有日志"""
        self._logs.clear()
        self.log_display.clear()
        self.log_cleared.emit()
        
    def _update_display(self) -> None:
        """更新显示"""
        # 保存当前滚动位置
        scrollbar = self.log_display.verticalScrollBar()
        was_at_bottom = scrollbar.value() == scrollbar.maximum()
        
        # 获取过滤后的日志
        filtered_logs = self._get_filtered_logs()
        
        # 清空并重新添加
        self.log_display.clear()
        
        for log_entry in filtered_logs:
            self._append_log_entry(log_entry)
            
        # 自动滚动到底部
        if self._auto_scroll or was_at_bottom:
            self._scroll_to_bottom()
            
    def _append_log_entry(self, log_entry: LogEntry) -> None:
        """
        添加日志条目到显示区域
        
        Args:
            log_entry: 日志条目
        """
        cursor = self.log_display.textCursor()
        cursor.movePosition(QTextCursor.End)
        
        # 设置颜色格式
        format = QTextCharFormat()
        color = self._get_level_color(log_entry.level)
        format.setForeground(color)
        
        cursor.setCharFormat(format)
        cursor.insertText(str(log_entry) + "\n")
        
    def _get_level_color(self, level: str) -> QColor:
        """
        获取日志级别对应的颜色
        
        Args:
            level: 日志级别
            
        Returns:
            QColor: 颜色
        """
        color_map = {
            LogLevel.DEBUG: QColor(128, 128, 128),    # 灰色
            LogLevel.INFO: QColor(0, 0, 0),           # 黑色
            LogLevel.WARNING: QColor(255, 140, 0),    # 橙色
            LogLevel.ERROR: QColor(255, 0, 0),        # 红色
            LogLevel.SUCCESS: QColor(0, 128, 0)       # 绿色
        }
        return color_map.get(level, QColor(0, 0, 0))
        
    def _get_filtered_logs(self) -> List[LogEntry]:
        """
        获取过滤后的日志
        
        Returns:
            List[LogEntry]: 过滤后的日志列表
        """
        filtered = self._logs
        
        # 按级别过滤
        level_filter = self.level_filter.currentText()
        if level_filter != "全部":
            filtered = [log for log in filtered if log.level == level_filter]
            
        # 按搜索文本过滤
        search_text = self.search_edit.text().lower()
        if search_text:
            filtered = [log for log in filtered 
                       if search_text in log.message.lower()]
            
        return filtered
        
    def _filter_logs(self) -> None:
        """过滤日志"""
        self._update_display()
        
    def _set_auto_scroll(self, enabled: bool) -> None:
        """
        设置自动滚动
        
        Args:
            enabled: 是否启用自动滚动
        """
        self._auto_scroll = enabled
        
    def _scroll_to_bottom(self) -> None:
        """滚动到底部"""
        scrollbar = self.log_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def _export_logs(self) -> None:
        """导出日志"""
        # TODO: 实现日志导出功能
        self.add_info("日志导出功能待实现")
        
    def get_log_count(self) -> int:
        """
        获取日志总数
        
        Returns:
            int: 日志总数
        """
        return len(self._logs)
        
    def get_logs_by_level(self, level: str) -> List[LogEntry]:
        """
        按级别获取日志
        
        Args:
            level: 日志级别
            
        Returns:
            List[LogEntry]: 指定级别的日志列表
        """
        return [log for log in self._logs if log.level == level]
        
    def set_max_logs(self, max_logs: int) -> None:
        """
        设置最大日志数量
        
        Args:
            max_logs: 最大日志数量
        """
        self._max_logs = max_logs
        
        # 如果当前日志数量超过限制，则截断
        if len(self._logs) > self._max_logs:
            self._logs = self._logs[-self._max_logs:]
            self._update_display()
