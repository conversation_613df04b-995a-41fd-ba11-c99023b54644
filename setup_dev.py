# {{CHENGQI:
# Action: Added
# Timestamp: [2025-01-27 11:30:00 +08:00]
# Reason: P1-SETUP-001 - 创建开发环境自动化配置脚本
# Principle_Applied: DRY原则 - 自动化重复的环境配置任务，KISS原则 - 简化开发环境搭建
# Optimization: 一键式环境配置，减少手动操作错误
# Architectural_Note (AR): 开发工具链自动化，提高开发效率
# Documentation_Note (DW): 开发环境配置脚本已创建
# }}

"""
开发环境自动化配置脚本
用于快速搭建项目开发环境
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major != 3 or version.minor != 11:
        print(f"警告：当前Python版本为 {version.major}.{version.minor}")
        print("推荐使用Python *********版本")
        return False
    return True


def create_virtual_environment():
    """创建虚拟环境"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("虚拟环境已存在，跳过创建")
        return True
    
    try:
        print("正在创建虚拟环境...")
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✓ 虚拟环境创建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 虚拟环境创建失败：{e}")
        return False


def install_dependencies():
    """安装项目依赖"""
    venv_python = Path("venv/Scripts/python.exe")
    if not venv_python.exists():
        print("✗ 虚拟环境不存在，请先创建虚拟环境")
        return False
    
    try:
        print("正在安装项目依赖...")
        subprocess.run([
            str(venv_python), "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✓ 依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖安装失败：{e}")
        return False


def create_config_directories():
    """创建配置目录"""
    directories = [
        "config",
        "logs", 
        "temp",
        "exports",
        "resources/icons",
        "resources/templates",
        "resources/examples",
        "resources/docs",
        "resources/chromedriver"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ 创建目录：{directory}")


def main():
    """主函数"""
    print("=" * 50)
    print("内网RPA工具 - 开发环境配置")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        print("建议使用Python *********版本以确保最佳兼容性")
    
    # 创建虚拟环境
    if not create_virtual_environment():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 创建配置目录
    create_config_directories()
    
    print("\n" + "=" * 50)
    print("开发环境配置完成！")
    print("=" * 50)
    print("\n下一步操作：")
    print("1. 激活虚拟环境：venv\\Scripts\\activate")
    print("2. 运行程序：python main.py")
    print("3. 运行测试：pytest")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
